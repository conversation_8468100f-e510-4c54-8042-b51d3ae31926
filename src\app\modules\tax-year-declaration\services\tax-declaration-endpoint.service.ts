import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { Observable } from 'rxjs';
import { SeHttpResponse, SeHttpService } from 'se-ui-components-mf-lib';
import {
  DeclarationTypes,
  PutTaxDeclarationData,
} from '../tax-year-declaration.model';

@Injectable({
  providedIn: 'root',
})
export class TaxYearDeclarationEndpointService {
  constructor(private httpService: SeHttpService) {
    // Empty constructor
  }

  getDeclarationTypes(): Observable<SeHttpResponse<DeclarationTypes[]>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/model`,
      clearExceptions: true,
    });
  }

  getTaxYearsByImpost(model: string): Observable<SeHttpResponse<string[]>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/model/${model}/exercici`,
      clearExceptions: true,
    });
  }

  putTaxDeclarationData(
    body: PutTaxDeclarationData,
    idTramit: string,
  ): Observable<SeHttpResponse> {
    return this.httpService.put({
      method: 'put',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${idTramit}`,
      body,
      clearExceptions: true,
    });
  }
}
