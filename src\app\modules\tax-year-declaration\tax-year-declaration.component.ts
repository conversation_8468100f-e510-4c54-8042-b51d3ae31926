import { Component, OnDestroy, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Constant, Models } from '@core/models/constants.enum';
import {
  Column,
  SeDropdownOption,
  SeModal,
  SeModalOutputEvents,
  SeModalService,
  SeProgressModal,
  iDocumentPadoct,
} from 'se-ui-components-mf-lib';
import {
  ContestableActDocument,
  DeclarationTypes,
  File,
  PutTaxDeclarationData,
  Substitutiva,
} from './tax-year-declaration.model';
import { TranslateService } from '@ngx-translate/core';
import { TaxYearDeclarationService } from './services/tax-declaration.service';
import { TaxYearDeclarationEndpointService } from './services/tax-declaration-endpoint.service';
import { BehaviorSubject, Subject, takeUntil } from 'rxjs';
import { Router } from '@angular/router';
import { AppRoutes } from '@core/models/app-routes.enum';
import { StoreService } from '@core/services';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-tax-year-declaration',
  templateUrl: './tax-year-declaration.component.html',
  styleUrls: [],
})
export class TaxYearDeclarationComponent implements OnInit, OnDestroy {
  // FORM
  protected componentForm: FormGroup;
  // OBSERVABLES CONTROL DESTROY
  private destroyed$: Subject<void> = new Subject();
  // FORM CONTROL VALUES
  private currentModelCode: string = '';
  private currentYear: number | undefined = undefined;
  protected optionsModelList: SeDropdownOption[] = [];
  protected optionsTaxYearList: SeDropdownOption[] = [];
  // FILE UPLOAD
  protected showPanelFileUpload: boolean = false;
  protected fileUploadPanelTitle: string = '';
  protected downloadFileTemplateUrl: string | null = null;
  protected readonly functionalModule: string = Constant.NAME;
  protected readonly acceptedFiles = ['csv'];
  protected readonly contestableActDocument: ContestableActDocument[] = [
    {
      type: Constant.TAX_DOCUMENT_TYPE,
      name: Constant.TAX_DOCUMENT_TYPE,
      description: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.FILE_UPLOAD_PANEL.NOM_DOCUMENT',
      ),
      allowedFiles: this.acceptedFiles,
    },
  ];
  protected get idTramit(): string | null | undefined {
    return this.storeService.idTramit;
  }
  // TABLE COLUMNS
  protected tableColumns: Column[] =
    this.taxYearDeclarationService.getTableColumns();
  protected modalTableColumns: Column[] =
    this.taxYearDeclarationService.getModalTableColumns();
  // DELETE FILE
  protected deleteFileByDocId$: Subject<string> = new Subject();
  // Substitutive
  protected showSubstitutivePanel: boolean = false;
  protected substitutiveData: Substitutiva | null = null;
  protected samePresenter: boolean = false;
  // Code Insurance
  protected showPanelCodeInsurance: boolean = false;

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private taxYearDeclarationService: TaxYearDeclarationService,
    private taxDeclarationEndpointService: TaxYearDeclarationEndpointService,
    private storeService: StoreService,
    private router: Router,
    private modalService: SeModalService,
  ) {
    this.componentForm = this.fb.group({
      model: ['', Validators.required],
      codiImpost: ['', Validators.required],
      modelCode: ['', Validators.required],
      exercici: ['', Validators.required],
      codiAsseguradora: ['', Validators.pattern(/^[A-Za-z]\d{4}$/)],
      substitutiva: this.fb.group({
        numJustificant: [null],
        declarant: [null],
        dataPresentacio: [null],
        estat: [null],
      }),
      anex: this.fb.group(
        {
          idPadoct: ['', Validators.required],
          idDocument: ['', Validators.required],
          nom: [''],
          pes: [''],
          descripcio: [''],
          tipusDocument: [''],
          extension: [''],
        },
        Validators.required,
      ),
    });
  }

  ngOnInit(): void {
    this.getDeclarationTypes();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.deleteFileByDocId$.complete();
  }

  private getDeclarationTypes(): void {
    this.taxDeclarationEndpointService
      .getDeclarationTypes()
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          if (response?.content) {
            response.content.map((model: DeclarationTypes) => {
              this.optionsModelList.push({
                id: `${model.model}_${model.codiImpost}`,
                label: model.descripcio,
              });
            });
          }
        },
        error: (error) => {
          console.error('Error fetching declaration types:', error);
        },
      });
  }

  private getModelDescription(modelCode: string): string {
    const model = this.optionsModelList.find(
      (option) => option.id === modelCode,
    );
    return model ? model.label : '';
  }

  protected onExerciciChange(event: number): void {
    if (
      this.currentYear &&
      this.componentForm.get('anex')?.get('idDocument')?.value
    ) {
      this.showDeleteDataWarningModal(true);
    } else {
      this.currentYear = event;
      this.saveData();
      this.searchSubstituteiveDeclaration();
      this.showSubstitutivePanel = true;
    }
    this.showPanelFileUpload = true;
  }

  protected onModelChange(event: string): void {
    if (this.componentForm.get('model')?.value) {
      this.showDeleteDataWarningModal();
    } else {
      this.setModelCode(event);
    }
  }

  private setModelCode(modelCode: string): void {
    this.currentModelCode = modelCode;
    const [model, codiImpost] = modelCode.split('_');
    this.componentForm.get('model')?.setValue(model);
    this.componentForm.get('codiImpost')?.setValue(codiImpost);
    this.getTaxYearsByImpost(model);
    this.fileUploadPanelTitle = this.getModelDescription(modelCode);
    this.verifyCodeInsurance(model);
    this.downloadFileTemplateUrl = this.translateService.instant(
      'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.FILE_UPLOAD_PANEL.DESCRIPTION_2',
      {
        url: this.taxYearDeclarationService.getModelUrl(
          model,
          this.translateService.currentLang as 'ca' | 'es',
        ),
      },
    );
  }

  private verifyCodeInsurance(model: string): void {
    if (model === Models.MODEL_673) {
      this.componentForm
        .get('codiAsseguradora')
        ?.setValidators([
          Validators.required,
          Validators.pattern(/^[A-Za-z]\d{4}$/),
        ]);
      this.showPanelCodeInsurance = true;
    } else {
      this.componentForm.get('codiAsseguradora')?.clearValidators();
      this.componentForm.get('codiAsseguradora')?.setValue('');
      this.showPanelCodeInsurance = false;
    }
  }

  protected onSubstitutiveChange(event: boolean): void {
    if (event) {
      this.componentForm.get('substitutiva')?.patchValue(this.substitutiveData);
      this.saveData();
      this.showPanelFileUpload = true;
    } else {
      this.componentForm.get('substitutiva')?.reset();
      this.showPanelFileUpload = false;
    }
  }

  private showDeleteDataWarningModal(maintainYear: boolean = false): void {
    const modal: SeModal = this.taxYearDeclarationService.getWarningModalData();

    const modalRef = this.modalService.openModal(modal);

    modalRef.componentInstance.modalOutputEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe((event: string) => {
        if (event === SeModalOutputEvents.MAIN_ACTION) {
          this.deleteTaxDeclarationInformation(maintainYear);
          this.setModelCode(this.componentForm.get('modelCode')?.value);
          this.searchSubstituteiveDeclaration();
          this.saveData();
          modalRef.close();
        } else {
          this.resetFormsValues(modalRef);
        }
      });

    modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        this.resetFormsValues(modalRef);
      });
  }

  private resetFormsValues(modalRef: NgbModalRef): void {
    this.componentForm.get('modelCode')?.setValue(this.currentModelCode);
    this.componentForm.get('exercici')?.setValue(this.currentYear);
    this.fileUploadPanelTitle = this.getModelDescription(this.currentModelCode);
    this.verifyCodeInsurance(this.componentForm.get('model')?.value);
    this.downloadFileTemplateUrl = this.translateService.instant(
      'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.FILE_UPLOAD_PANEL.DESCRIPTION_2',
      {
        url: this.taxYearDeclarationService.getModelUrl(
          this.componentForm.get('model')?.value,
          this.translateService.currentLang as 'ca' | 'es',
        ),
      },
    );
    modalRef.close();
  }

  private deleteTaxDeclarationInformation(maintainYear: boolean = false): void {
    if (!maintainYear) {
      this.componentForm.get('exercici')?.setValue('');
      this.showPanelFileUpload = false;
    }
    const fileId: string = this.componentForm
      .get('anex')
      ?.get('idDocument')?.value;
    this.deleteFileByDocId$.next(fileId);
  }

  private getTaxYearsByImpost(model: string): void {
    this.taxDeclarationEndpointService
      .getTaxYearsByImpost(model)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          if (response?.content) {
            this.optionsTaxYearList = response.content.map((year: string) => ({
              id: year,
              label: year,
            }));
          }
        },
        error: (error) => {
          console.error('Error fetching tax years:', error);
        },
      });
  }

  protected openProgressModal(): void {
    const progressValue$ = new BehaviorSubject<number>(1);

    const progressModal: SeProgressModal = {
      interval: 2,
      subtitle: '',
      message: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.PROGRESS_MODAL.DESCRIPTION',
      ),
      customButton: {
        label: this.translateService.instant('UI_COMPONENTS.BUTTONS.CANCEL'),
        size: 'small',
        btnTheme: 'trueOnlyText',
      },
      progressValue$: progressValue$,
    };

    const modalRef = this.modalService.openProgressModal(
      progressModal.interval!,
      progressModal.message!,
      progressModal.subtitle,
      progressModal.progressValue$,
      progressModal.customButton ?? undefined,
    );

    // TODO Intervalo para pruebas hasta que tengamos el servicio real
    let value = 0;
    const intervalId = setInterval(() => {
      value += 20;
      progressValue$!.next(value);
      modalRef.componentInstance.progressValue = progressValue$;
      if (value >= 100) {
        progressValue$!.next(100);
        progressValue$!.complete();
        clearInterval(intervalId);
        modalRef.close();
        this.openSuccessModal();
      }
    }, 1000);

    modalRef.componentInstance.onCustomButton
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        progressValue$!.next(100);
        progressValue$!.complete();
        clearInterval(intervalId);
        modalRef.close();
      });

    //this.checkValidationProgress(modalRef, csvUploadBody, this.storeService.idTramit!); TODO LLAMADA REAL
  }

  /*  private checkValidationProgress = (
    modalRef: NgbModalRef,
    csvUploadBody: PutCsvUploaded,
    idTramit: string
  ): void => {
    modalRef.componentInstance.intervalOutput
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        this.taxDeclarationEndpointService
          .putFileCsvUploaded(csvUploadBody, idTramit)
          .pipe(takeUntil(this.destroyed$))
          .subscribe({
            next: (response) => {
             console.log(response);
             
            },
            error: () => {
            },
          });
      });
  }; */

  protected onFilesLoaded(event: Event): void {
    const documentsAdded: iDocumentPadoct[] =
      (event as CustomEvent).detail || [];
    if (documentsAdded.length > 0) {
      const anexData = documentsAdded[0];
      this.componentForm.get('anex')?.patchValue({
        idPadoct: anexData.idPadoct,
        nom: anexData.nom,
        descripcio: anexData.description,
        pes: anexData.size,
        idDocument: anexData.id,
        extension: anexData.format?.split('/')[1],
        tipusDocument: Constant.TAX_DOCUMENT_TYPE,
      });
      this.saveData();
    } else {
      this.componentForm.get('anex')?.reset();
    }
  }

  protected goBack(): void {
    this.router.navigate([AppRoutes.PARTICIPANTS]);
  }

  private searchSubstituteiveDeclaration(): void {
    // Todo - Endpoint substitutiva
    // Simulamos que la respuesta viene vacia
    const response: Substitutiva | null = null;
    if (
      !response &&
      this.componentForm.get('exercici')?.value < Constant.NEW_SUBSTITUTIVE_YEAR
    ) {
      this.substitutiveData = null;
      this.samePresenter = false;
      this.showSubstitutivePanel = true;
      this.componentForm.get('substitutiva')?.reset();
    }
  }

  private saveData(): void {
    const file: File = this.componentForm.get('anex')?.value;
    const body: PutTaxDeclarationData = {
      model: this.componentForm.get('model')?.value,
      exercici: this.componentForm.get('exercici')?.value,
      substitutiva: this.componentForm.get('substitutiva')?.value,
      codiImpost: this.componentForm.get('codiImpost')?.value,
      fitxer: file,
    };
    this.taxDeclarationEndpointService
      .putTaxDeclarationData(body, this.idTramit!)
      .pipe(takeUntil(this.destroyed$))
      .subscribe();
  }

  private openSuccessModal(): void {
    const modal: SeModal = {
      severity: 'success',
      title: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.PROGRESS_MODAL.SUCCESS.TITLE',
      ),
      subtitle: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.PROGRESS_MODAL.SUCCESS.DESCRIPTION',
      ),
      closable: true,
      closableLabel: this.translateService.instant(
        'UI_COMPONENTS.BUTTONS.CONTINUE',
      ),
      size: 'lg',
    };

    const modalRef = this.modalService.openModal(modal);
    modalRef.componentInstance.modalOutputEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe((event: string) => {
        if (event === SeModalOutputEvents.MAIN_ACTION) {
          modalRef.close();
          this.submit();
        }
      });
  }

  protected submit(): void {
    // TODO - routing to next step
  }
}
